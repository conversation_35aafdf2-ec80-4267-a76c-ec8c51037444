import MainLayout from '@/components/layout/MainLayout';
import { Hero, About, Skills } from '@/components/sections';
import { Button } from '@/components/ui';
import { PERSONAL_INFO } from '@/lib/constants';

export default function Home() {
  return (
    <MainLayout>
      {/* Hero Section */}
      <Hero />

      {/* About Section */}
      <About />

      {/* Skills Section */}
      <Skills />

      {/* Temporary content for other sections */}
      <div className="min-h-screen">

        {/* About Section */}
        <section id="about" className="min-h-screen flex items-center justify-center bg-muted/20">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-5xl font-bold text-foreground mb-6">
              About Me
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              {PERSONAL_INFO.bio}
            </p>
          </div>
        </section>

        {/* Projects Section */}
        <section id="projects" className="min-h-screen flex items-center justify-center">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-5xl font-bold text-foreground mb-6">
              My Projects
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Showcasing my work including revisionofficial.com and the BEINCOURT dashboard.
            </p>
          </div>
        </section>

        {/* Experience Section */}
        <section id="experience" className="min-h-screen flex items-center justify-center bg-muted/20">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-5xl font-bold text-foreground mb-6">
              Experience
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              My journey through Computer Science at Cal Poly and professional development.
            </p>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" className="min-h-screen flex items-center justify-center">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-5xl font-bold text-foreground mb-6">
              Get In Touch
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto mb-8">
              Ready to work together? Let's create something amazing.
            </p>
            <Button variant="primary" size="lg">
              Contact Me
            </Button>
          </div>
        </section>
      </div>
    </MainLayout>
  );
}
